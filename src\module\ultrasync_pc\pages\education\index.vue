<template>
    <div class="education-main">
        <div class="tab-container">
            <div class="custom-tabs-header">
                <el-tabs v-model="activeTab" class="education-tabs education-tabs--large" @tab-click="handleTabClick">
                    <el-tab-pane :label="lang.mindray_library" name="library"></el-tab-pane>
                    <el-tab-pane label="直播培训" name="live_training"></el-tab-pane>
                    <el-tab-pane label="瑞影·AI+" name="ai_plus"></el-tab-pane>
                    <el-tab-pane :label="lang.case_database_title" name="case_database"></el-tab-pane>
                    <el-tab-pane :label="lang.cloud_exam" name="cloud_exam"></el-tab-pane>
                    <el-tab-pane label="技能认证" name="smart_tech_training"></el-tab-pane>
                </el-tabs>
            </div>

            <div class="tab-content">
                <keep-alive>
                    <router-view></router-view>
                </keep-alive>
            </div>
        </div>
    </div>
</template>

<script>
import base from "../../lib/base";
import Tool from "@/common/tool";

export default {
    mixins: [base],
    name: "EducationMain",
    components: {},
    data() {
        return {
            activeTab: "library",
            lastActiveTab: "library",
        };
    },
    watch: {
        $route: {
            immediate: true,
            handler(to) {
                // 如果路由变化时包含具体的tab路径，则更新activeTab
                if (this.setActiveTabByRoute(to.path)) {
                    // 保存当前选中的tab到localStorage
                    this.saveActiveTabToStorage(this.activeTab);
                }
            },
        },
    },
    created() {
        this.initActiveTab();
    },
    methods: {
        // 获取上次保存的tab
        getLastActiveTabFromStorage() {
            try {
                return localStorage.getItem('education_last_active_tab') || 'library';
            } catch (error) {
                console.error('Error reading education_last_active_tab from localStorage:', error);
                return 'library';
            }
        },

        // 保存当前选中的tab到localStorage
        saveActiveTabToStorage(tabName) {
            try {
                localStorage.setItem('education_last_active_tab', tabName);
            } catch (error) {
                console.error('Error saving education_last_active_tab to localStorage:', error);
            }
        },

        // 初始化activeTab
        initActiveTab() {
            const currentPath = this.$route.path;

            // 如果当前路径包含具体的tab路径，则使用路径中的tab
            if (this.setActiveTabByRoute(currentPath)) {
                return;
            }

            // 如果路径中没有具体的tab，则使用上次保存的tab
            const lastActiveTab = this.getLastActiveTabFromStorage();
            this.activeTab = lastActiveTab;
            this.lastActiveTab = lastActiveTab;
        },

        setActiveTabByRoute(path) {
            if (path.includes("/education/library")) {
                this.activeTab = "library";
                this.lastActiveTab = "library";
                return true;
            } else if (path.includes("/education/live_training")) {
                this.activeTab = "live_training";
                this.lastActiveTab = "live_training";
                return true;
            } else if (path.includes("/education/ai_plus")) {
                this.activeTab = "ai_plus";
                this.lastActiveTab = "ai_plus";
                return true;
            } else if (path.includes("/education/case_database")) {
                this.activeTab = "case_database";
                this.lastActiveTab = "case_database";
                return true;
            } else if (path.includes("/education/cloud_exam")) {
                this.activeTab = "cloud_exam";
                this.lastActiveTab = "cloud_exam";
                return true;
            } else if (path.includes("/education/smart_tech_training")) {
                this.activeTab = "smart_tech_training";
                this.lastActiveTab = "smart_tech_training";
                return true;
            }
            return false;
        },

        // 导航到指定tab
        async navigateToTab(tabName) {
            const cid = this.$route.params.cid;
            let targetRoute;

            if (cid) {
                // 从聊天窗口进入的路由
                targetRoute = `/main/index/chat_window/${cid}/education/${tabName}`;
            } else {
                // 直接访问的路由
                targetRoute = `/main/education/${tabName}`;
            }

            await Tool.loadModuleRouter(targetRoute);
        },
        async handleTabClick(tab) {
            const cid = this.$route.params.cid;
            if(tab.name === this.lastActiveTab){
                return
            }

            let targetRoute;
            if (cid) {
                // 从聊天窗口进入的路由
                targetRoute = `/main/index/chat_window/${cid}/education/${tab.name}`;
            } else {
                // 直接访问的路由
                targetRoute = `/main/education/${tab.name}`;
            }

            await Tool.loadModuleRouter(targetRoute);
            this.lastActiveTab = tab.name;

            // 保存当前选中的tab到localStorage
            this.saveActiveTabToStorage(tab.name);
        },
    },
};
</script>

<style lang="scss" scoped>
@import '@/module/ultrasync_pc/style/education.scss';

.education-main {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 20;
    display: flex;
    flex-direction: column;
    background-color: #f7f9fc;
}

.tab-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: #fff;
}

.custom-tabs-header {
    display: flex;
    align-items: center;
    background-color: #fff;
    border-bottom: 1px solid #ebeef5;
    padding: 0;
    position: relative;
    height: 60px; // 设置固定高度与tab项一致
    padding-left: 40px;
}

.education-tabs {
    flex: 1;
    width: 100%;
    height: 100%;
}

.tab-content {
    flex: 1;
    overflow: hidden;
    position: relative;
}

.coming-soon {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;

    h1 {
        font-size: 28px;
        color: #909399;
        font-weight: 400;
    }
}
</style>
